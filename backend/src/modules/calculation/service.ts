import { Calculation, ICalculation, ICalculationInput, ICalculationResult, IMaritimeCalculationInput, IMaritimeCalculationResult, IFeeBreakdown, VesselType, FlagCategory, LightDueOperationType } from './model';
import mongoose from 'mongoose';

export class CalculationService {
  static calculateProforma(inputData: ICalculationInput[]): ICalculationResult {
    let toplamMaliyet = 0;
    let toplamKdvTutari = 0;
    let toplamIskontoTutari = 0;
    
    const detayliHesaplama: any = {
      kalemler: [],
      ozet: {}
    };

    // Her bir kalem için hesaplama
    inputData.forEach((item, index) => {
      const {
        maliyetGrubu,
        miktar,
        birimFiyat,
        kdvOrani,
        iskontoOrani = 0,
        ozelDurum,
        aciklama
      } = item;

      // Ara toplam (iskonto öncesi)
      const araToplam = miktar * birimFiyat;
      
      // İskonto tutarı
      const iskontoTutari = (araToplam * iskontoOrani) / 100;
      
      // İskonto sonrası tutar
      const iskontoSonrasiTutar = araToplam - iskontoTutari;
      
      // KDV tutarı
      const kdvTutari = (iskontoSonrasiTutar * kdvOrani) / 100;
      
      // Net tutar (KDV dahil)
      const netTutar = iskontoSonrasiTutar + kdvTutari;

      // Detaylı hesaplama kaydı
      const kalemDetay = {
        siraNo: index + 1,
        maliyetGrubu,
        miktar,
        birimFiyat,
        araToplam,
        iskontoOrani,
        iskontoTutari,
        iskontoSonrasiTutar,
        kdvOrani,
        kdvTutari,
        netTutar,
        ozelDurum,
        aciklama
      };

      detayliHesaplama.kalemler.push(kalemDetay);

      // Toplamları güncelle
      toplamMaliyet += araToplam;
      toplamKdvTutari += kdvTutari;
      toplamIskontoTutari += iskontoTutari;
    });

    const netTutar = toplamMaliyet - toplamIskontoTutari + toplamKdvTutari;

    // Özet bilgileri
    detayliHesaplama.ozet = {
      toplamKalem: inputData.length,
      toplamMaliyet,
      toplamIskontoTutari,
      iskontoSonrasiToplam: toplamMaliyet - toplamIskontoTutari,
      toplamKdvTutari,
      netTutar,
      ortalamaBirimFiyat: toplamMaliyet / inputData.reduce((sum, item) => sum + item.miktar, 0),
      ortalama_kdv_orani: toplamKdvTutari / (toplamMaliyet - toplamIskontoTutari) * 100
    };

    return {
      toplamMaliyet,
      kdvTutari: toplamKdvTutari,
      iskontoTutari: toplamIskontoTutari,
      netTutar,
      detayliHesaplama
    };
  }

  static async createCalculation(userId: string, calculationData: {
    calculationType: 'proforma' | 'maliyet' | 'kar_zarar';
    inputData: ICalculationInput[];
    title?: string;
    description?: string;
    isPublic?: boolean;
    tags?: string[];
  }): Promise<ICalculation> {
    // Hesaplama yap
    let result: ICalculationResult;
    
    switch (calculationData.calculationType) {
      case 'proforma':
        result = this.calculateProforma(calculationData.inputData);
        break;
      case 'maliyet':
        // Maliyet hesaplama algoritması (PROFORMA klavuzuna göre eklenecek)
        result = this.calculateProforma(calculationData.inputData); // Şimdilik aynı
        break;
      case 'kar_zarar':
        // Kar-zarar hesaplama algoritması (PROFORMA klavuzuna göre eklenecek)
        result = this.calculateProforma(calculationData.inputData); // Şimdilik aynı
        break;
      default:
        throw new Error('Geçersiz hesaplama türü');
    }

    // Veritabanına kaydet
    const calculation = await Calculation.create({
      userId,
      ...calculationData,
      result
    });

    return calculation;
  }

  static async getCalculationById(id: string, userId?: string): Promise<ICalculation | null> {
    const query: any = { _id: id };
    
    // Eğer userId belirtilmişse, sadece o kullanıcının hesaplamalarını getir
    // veya public hesaplamaları getir
    if (userId) {
      query.$or = [
        { userId },
        { isPublic: true }
      ];
    } else {
      query.isPublic = true; // Sadece public hesaplamalar
    }

    return await Calculation.findOne(query).populate('userId', 'name email');
  }

  static async getUserCalculations(userId: string, filters: {
    page?: number;
    limit?: number;
    calculationType?: string;
    search?: string;
    tags?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<{
    calculations: ICalculation[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  }> {
    const {
      page = 1,
      limit = 10,
      calculationType,
      search,
      tags,
      sortBy = 'calculationDate',
      sortOrder = 'desc'
    } = filters;

    const query: any = { userId };

    // Filtreleri uygula
    if (calculationType) {
      query.calculationType = calculationType;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query.tags = { $in: tagArray };
    }

    const skip = (page - 1) * limit;
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [calculations, totalCount] = await Promise.all([
      Calculation.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .populate('userId', 'name email'),
      Calculation.countDocuments(query)
    ]);

    return {
      calculations,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page
    };
  }

  static async updateCalculation(id: string, userId: string, updateData: {
    title?: string;
    description?: string;
    isPublic?: boolean;
    tags?: string[];
  }): Promise<ICalculation | null> {
    return await Calculation.findOneAndUpdate(
      { _id: id, userId },
      updateData,
      { new: true, runValidators: true }
    );
  }

  static async deleteCalculation(id: string, userId: string): Promise<boolean> {
    const result = await Calculation.deleteOne({ _id: id, userId });
    return result.deletedCount > 0;
  }

  static async duplicateCalculation(id: string, userId: string): Promise<ICalculation | null> {
    const originalCalculation = await Calculation.findOne({ _id: id, userId });
    
    if (!originalCalculation) {
      return null;
    }

    const duplicatedCalculation = await Calculation.create({
      userId,
      calculationType: originalCalculation.calculationType,
      inputData: originalCalculation.inputData,
      result: originalCalculation.result,
      title: `${originalCalculation.title || 'Hesaplama'} (Kopya)`,
      description: originalCalculation.description,
      isPublic: false, // Kopyalar varsayılan olarak private
      tags: originalCalculation.tags
    });

    return duplicatedCalculation;
  }

  static async getCalculationStats(userId: string): Promise<{
    totalCalculations: number;
    calculationsByType: { [key: string]: number };
    totalNetAmount: number;
    averageNetAmount: number;
    lastCalculationDate: Date | null;
    mostUsedTags: string[];
  }> {
    const [
      totalCalculations,
      calculationsByType,
      totalNetAmountResult,
      lastCalculation,
      tagsAggregation
    ] = await Promise.all([
      Calculation.countDocuments({ userId }),
      
      Calculation.aggregate([
        { $match: { userId: new mongoose.Types.ObjectId(userId) } },
        { $group: { _id: '$calculationType', count: { $sum: 1 } } }
      ]),
      
      Calculation.aggregate([
        { $match: { userId: new mongoose.Types.ObjectId(userId) } },
        { $group: { _id: null, total: { $sum: '$result.netTutar' }, avg: { $avg: '$result.netTutar' } } }
      ]),
      
      Calculation.findOne({ userId }).sort({ calculationDate: -1 }),
      
      Calculation.aggregate([
        { $match: { userId: new mongoose.Types.ObjectId(userId) } },
        { $unwind: '$tags' },
        { $group: { _id: '$tags', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ])
    ]);

    const calculationsByTypeObj: { [key: string]: number } = {};
    calculationsByType.forEach((item: any) => {
      calculationsByTypeObj[item._id] = item.count;
    });

    const totalNetAmount = totalNetAmountResult[0]?.total || 0;
    const averageNetAmount = totalNetAmountResult[0]?.avg || 0;

    const mostUsedTags = tagsAggregation.map((item: any) => item._id);

    return {
      totalCalculations,
      calculationsByType: calculationsByTypeObj,
      totalNetAmount,
      averageNetAmount,
      lastCalculationDate: lastCalculation?.calculationDate || null,
      mostUsedTags
    };
  }

  static async getPublicCalculations(filters: {
    page?: number;
    limit?: number;
    calculationType?: string;
    search?: string;
    tags?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<{
    calculations: ICalculation[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  }> {
    const {
      page = 1,
      limit = 10,
      calculationType,
      search,
      tags,
      sortBy = 'calculationDate',
      sortOrder = 'desc'
    } = filters;

    const query: any = { isPublic: true };

    // Filtreleri uygula
    if (calculationType) {
      query.calculationType = calculationType;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query.tags = { $in: tagArray };
    }

    const skip = (page - 1) * limit;
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [calculations, totalCount] = await Promise.all([
      Calculation.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .populate('userId', 'name'),
      Calculation.countDocuments(query)
    ]);

    return {
      calculations,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page
    };
  }

  // Round GRT/NRT up to next 1000
  private roundToNext1000(tonnage: number): number {
    return Math.ceil(tonnage / 1000) * 1000;
  }

  // Validate input data
  private validateInput(input: IMaritimeCalculationInput): string[] {
    const errors: string[] = [];

    // Required fields validation
    if (!input.portName?.trim()) errors.push('Liman adı gereklidir');
    if (!input.portLocation?.trim()) errors.push('Liman lokasyonu gereklidir');
    if (!input.vesselType) errors.push('Gemi tipi gereklidir');
    if (!input.flagCategory) errors.push('Bayrak kategorisi gereklidir');
    if (!input.cargoType) errors.push('Yük tipi gereklidir');
    if (!input.portCallType) errors.push('Liman çağrısı tipi gereklidir');

    // Numeric validations
    if (!input.grossRegisterTonnage || input.grossRegisterTonnage <= 0) {
      errors.push('Geçerli bir Gross Register Tonnage (GRT) değeri gereklidir');
    }
    if (!input.netRegisterTonnage || input.netRegisterTonnage <= 0) {
      errors.push('Geçerli bir Net Register Tonnage (NRT) değeri gereklidir');
    }
    if (!input.usdTryRate || input.usdTryRate <= 0) {
      errors.push('Geçerli bir USD/TRY kuru gereklidir');
    }

    // Days at quay validation based on port call type
    if (input.portCallType === 'bosphorus-transit' || input.portCallType === 'canakkale-transit') {
      // Transit only - days at quay should be 0
      if (input.daysAtQuay > 0) {
        errors.push('Sadece geçiş için rıhtım günü sayısı 0 olmalıdır');
      }
    } else {
      // Port call - days at quay should be positive
      if (!input.daysAtQuay || input.daysAtQuay <= 0) {
        errors.push('Liman çağrısı için rıhtım günü sayısı pozitif olmalıdır');
      }
    }

    // DWT is now required and should be positive
    if (!input.deadweightTonnage || input.deadweightTonnage <= 0) {
      errors.push('Geçerli bir Deadweight Tonnage (DWT) değeri gereklidir');
    }
    if (input.cargoQuantity && input.cargoQuantity < 0) {
      errors.push('Yük miktarı negatif olamaz');
    }
    if (input.eurUsdRate && input.eurUsdRate <= 0) {
      errors.push('EUR/USD kuru pozitif olmalıdır');
    }

    // Cargo validation
    if (input.cargoQuantity && input.cargoQuantity > 0 && !input.cargoCategory) {
      errors.push('Yük miktarı belirtildiğinde yük kategorisi gereklidir');
    }

    return errors;
  }

  // Calculate pilotage fee
  private calculatePilotageFee(
    roundedGRT: number,
    vesselType: VesselType,
    flagCategory: FlagCategory,
    isIzmitKorfezi: boolean,
    isDangerous: boolean
  ): any {
    let baseRateFirstGT: number;
    let additionalRatePerGT: number;
    const numberOfPilots = 2; // inbound and outbound

    if (isIzmitKorfezi) {
      // ETAP pilotage for İzmit Körfezi
      baseRateFirstGT = 152;
      additionalRatePerGT = 73;
    } else if (flagCategory === 'cabotage') {
      // CABOTAGE (TURKISH FLAG) - Sabit oranlar klavuza göre
      baseRateFirstGT = 70;
      additionalRatePerGT = 25;
    } else {
      // FOREIGN ve TURKISH (international) bayraklar için vessel type'a göre
      switch (vesselType) {
        case 'passenger':
        case 'ro-ro-ro-pax':
        case 'car-carrier':
          baseRateFirstGT = 116;
          additionalRatePerGT = 46;
          break;
        case 'container':
          baseRateFirstGT = 153;
          additionalRatePerGT = 65;
          break;
        case 'lng-tanker':
        case 'bunker-tanker':
        case 'other':
        default:
          baseRateFirstGT = 197;
          additionalRatePerGT = 81;
          break;
      }
    }

    const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
    const baseAmount = (baseRateFirstGT + (additionalGT * additionalRatePerGT)) * numberOfPilots;

    // Tehlikeli yük çarpanı: 1.30 ile çarpma (klavuza göre)
    let total = isDangerous ? baseAmount * 1.30 : baseAmount;
    const dangerousSurcharge = isDangerous ? total - baseAmount : 0;

    // Türk bayrağı indirimi: %20 indirim (sadece 'turkish' flag için, cabotage değil)
    let turkishFlagDiscount = 0;
    if (flagCategory === 'turkish') {
      turkishFlagDiscount = total * 0.20;
      total = total - turkishFlagDiscount;
    }

    return {
      baseAmount,
      numberOfPilots,
      dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
      turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
      total,
      calculation: `${roundedGRT/1000} GT: First 1000 GT ${baseRateFirstGT} USD${additionalGT > 0 ? ` + ${additionalGT} x ${additionalRatePerGT} USD` : ''} x ${numberOfPilots} pilots${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}${flagCategory === 'turkish' ? ' - 20% Turkish flag discount' : ''}`
    };
  }

  // Calculate tugboat fee
  private calculateTugboatFee(
    roundedGRT: number,
    vesselType: VesselType,
    flagCategory: FlagCategory,
    isDangerous: boolean
  ): any {
    // Determine number of tugboats needed - PROFORMA klavuzuna göre düzeltilmiş
    let numberOfTugs: number;
    if (roundedGRT <= 2000) {
      numberOfTugs = 2; // 1 in, 1 out (1000 GRT altı hariç)
    } else if (roundedGRT <= 5000) {
      numberOfTugs = 4; // 2 in, 2 out (2001-5000 GRT)
    } else if (roundedGRT <= 15000) {
      numberOfTugs = 6; // 3 in, 3 out (5001-15000 GRT) - DÜZELTME: Önceden 4'tü, şimdi 6
    } else if (roundedGRT <= 30000) {
      numberOfTugs = 4; // 2 in, 2 out (15001-30000 GRT) - UPDATED: Changed from 6 to 4
    } else {
      numberOfTugs = 6; // 3 in, 3 out (30000+ GRT)
    }

    let baseRateFirstGT: number;
    let additionalRatePerGT: number;

    if (flagCategory === 'cabotage') {
      // CABOTAGE (TURKISH FLAG) - Sabit oranlar klavuza göre
      baseRateFirstGT = 119;
      additionalRatePerGT = 25;
    } else {
      // FOREIGN ve TURKISH (international) bayraklar için vessel type'a göre
      switch (vesselType) {
        case 'passenger':
        case 'ro-ro-ro-pax':
        case 'car-carrier':
          baseRateFirstGT = 224;
          additionalRatePerGT = 40;
          break;
        case 'container':
          baseRateFirstGT = 299;
          additionalRatePerGT = 56;
          break;
        case 'lng-tanker':
        case 'bunker-tanker':
        case 'other':
        default:
          baseRateFirstGT = 373;
          additionalRatePerGT = 70;
          break;
      }
    }

    const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
    const ratePerTug = baseRateFirstGT + (additionalGT * additionalRatePerGT);
    const baseAmount = ratePerTug * numberOfTugs;

    // Tehlikeli yük çarpanı: 1.30 ile çarpma (klavuza göre)
    let total = isDangerous ? baseAmount * 1.30 : baseAmount;
    const dangerousSurcharge = isDangerous ? total - baseAmount : 0;

    // Türk bayrağı indirimi: %20 indirim (sadece 'turkish' flag için, cabotage değil)
    let turkishFlagDiscount = 0;
    if (flagCategory === 'turkish') {
      turkishFlagDiscount = total * 0.20;
      total = total - turkishFlagDiscount;
    }

    return {
      numberOfTugs,
      ratePerTug,
      baseAmount,
      dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
      turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
      total,
      calculation: `${roundedGRT/1000} GT: ${ratePerTug} USD/tugboat x ${numberOfTugs} tugboats${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}${flagCategory === 'turkish' ? ' - 20% Turkish flag discount' : ''}`
    };
  }

  // Calculate mooring fee
  private calculateMooringFee(
    roundedGRT: number,
    flagCategory: FlagCategory,
    vesselType: VesselType,
    isDangerous: boolean
  ): any {
    let baseRateFirstGT: number;
    let additionalRatePerGT: number;

    if (flagCategory === 'cabotage') {
      // CABOTAGE (TURKISH FLAG) - Klavuza göre
      baseRateFirstGT = 11;
      additionalRatePerGT = 6;
    } else {
      // FOREIGN ve TURKISH (international) bayraklar için
      baseRateFirstGT = 22;
      additionalRatePerGT = 11;
    }

    const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
    const singleOperationRate = baseRateFirstGT + (additionalGT * additionalRatePerGT);

    // Bağlama ve çözme (x2) - klavuza göre
    const baseAmount = singleOperationRate * 2;

    // Tehlikeli yük çarpanı: 1.30 ile çarpma (klavuza göre)
    let total = isDangerous ? baseAmount * 1.30 : baseAmount;
    const dangerousSurcharge = isDangerous ? total - baseAmount : 0;

    // Türk bayrağı indirimi: %20 indirim (sadece 'turkish' flag için, cabotage değil)
    let turkishFlagDiscount = 0;
    if (flagCategory === 'turkish') {
      turkishFlagDiscount = total * 0.20;
      total = total - turkishFlagDiscount;
    }

    return {
      berthingFee: singleOperationRate,
      unberthingFee: singleOperationRate,
      baseAmount,
      dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
      turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
      total,
      calculation: `${roundedGRT/1000} GT: ${singleOperationRate} USD x 2 (berthing + unberthing)${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}${flagCategory === 'turkish' ? ' - 20% Turkish flag discount' : ''}`
    };
  }

  // Calculate quay due
  private calculateQuayDue(
    roundedGRT: number,
    flagCategory: FlagCategory,
    vesselType: VesselType,
    daysAtQuay: number,
    isDangerous: boolean
  ): any {
    let baseRateFirstGT: number;
    let additionalRatePerGT: number;

    switch (flagCategory) {
      case 'cabotage':
        baseRateFirstGT = 13;
        additionalRatePerGT = 8;
        break;
      case 'turkish':
        baseRateFirstGT = 19;
        additionalRatePerGT = 19;
        break;
      case 'foreign':
        baseRateFirstGT = 25;
        additionalRatePerGT = 25;
        break;
    }

    const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
    const dailyRate = baseRateFirstGT + (additionalGT * additionalRatePerGT);
    const baseAmount = dailyRate * daysAtQuay;

    // Tehlikeli yük çarpanı: 1.30 ile çarpma (klavuza göre)
    let total = isDangerous ? baseAmount * 1.30 : baseAmount;
    const dangerousSurcharge = isDangerous ? total - baseAmount : 0;

    // Türk bayrağı indirimi: %25 indirim (sadece 'turkish' flag için, cabotage değil)
    let turkishFlagDiscount = 0;
    if (flagCategory === 'turkish') {
      turkishFlagDiscount = total * 0.25;
      total = total - turkishFlagDiscount;
    }

    let calculation = `${roundedGRT/1000} GT: ${dailyRate} USD/day x ${daysAtQuay} days`;
    if (isDangerous) {
      calculation += ' x 1.30 (dangerous cargo surcharge)';
    }
    if (flagCategory === 'turkish') {
      calculation += ' - 25% (Turkish flag discount)';
    }

    return {
      dailyRate,
      days: daysAtQuay,
      baseAmount,
      dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
      turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
      total,
      calculation
    };
  }

  // Calculate garbage collection fee (klavuzdaki tabloya göre)
  private calculateGarbageCollectionFee(roundedGRT: number, eurUsdRate?: number): any {
    let amountEUR: number;
    
    // PROFORMA klavuzundaki tablo (Resim referansı: Aspose.Words.6c0603c7-339b-449d-aca4-bf85ba548868.001.jpeg)
    if (roundedGRT <= 1000) {
      amountEUR = 80;
    } else if (roundedGRT >= 1001 && roundedGRT <= 5000 ) {
      amountEUR = 140;
    } else if (roundedGRT >= 5001 && roundedGRT <= 10000) {
      amountEUR = 210;
    } else if (roundedGRT >= 10001 && roundedGRT <= 15000) {
      amountEUR = 250;
    } else if (roundedGRT >= 15001 && roundedGRT <= 20000) {
      amountEUR = 300;
    } else if (roundedGRT >= 20001 && roundedGRT <= 25000) {
      amountEUR = 350;
    } else if (roundedGRT >= 25001 && roundedGRT <= 35000) {
      amountEUR = 400;
    } else if (roundedGRT >= 35001 && roundedGRT <= 60000) {
      amountEUR = 540;
    }   else {
      amountEUR = 720; // 600000+ GRT
    }

    const usdAmount = eurUsdRate ? amountEUR * eurUsdRate : amountEUR;
    
    return {
      amount: amountEUR,
      currency: 'EUR' as const,
      usdAmount,
      calculation: `${roundedGRT} GT: ${amountEUR} EUR${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
    };
  }

  // Calculate sanitary fee
  private calculateSanitaryFee(
    nrt: number,
    flagCategory: FlagCategory,
    usdTryRate: number,
    isTransitOnly?: boolean
  ): any {
    let baseAmountTL: number;

    if (isTransitOnly) {
      baseAmountTL = nrt * 0.3803;
    } else {
      switch (flagCategory) {
        case 'foreign':
          baseAmountTL = nrt * 17.27;
          break;
        case 'cabotage':
        case 'turkish':
          if (nrt >= 50000 && nrt <= 250000) {
            baseAmountTL = nrt * 13.73;
          } else if (nrt > 250000) {
            baseAmountTL = nrt * 31.00;
          } else {
            baseAmountTL = nrt * 13.73; // Default for under 50,000 NRT
          }
          break;
      }
    }

    const usdAmount = baseAmountTL / usdTryRate;

    return {
      amount: usdAmount,
      baseAmountTL,
      usdAmount,
      calculation: `${nrt} NRT x ${baseAmountTL/nrt} TL = ${baseAmountTL.toFixed(2)} TL (${usdAmount.toFixed(2)} USD)`
    };
  }

  // Determine Light Due operation type based on port call type and context
  private determineLightDueOperationType(
    portCallType: string,
    flagCategory: FlagCategory,
    isTransit?: boolean
  ): LightDueOperationType {
    // All flag categories now support special calculations
    if (isTransit) {
      // For transit operations, determine based on port call type
      switch (portCallType) {
        case 'bosphorus-transit':
          return 'bosphorus-entry'; // Default to entry for transit
        case 'canakkale-transit':
          return 'dardanelles-entry'; // Default to entry for transit
        default:
          return 'standard';
      }
    } else {
      // For port calls, use port entry/exit operations
      // In practice, port calls typically involve both entry and exit
      // For now, we'll use port-entry as default, but this could be enhanced
      // to calculate both entry and exit separately if needed
      return 'port-entry';
    }
  }

  // Helper method to get all applicable Light Due operations for a port call
  private getAllLightDueOperations(
    portCallType: string,
    flagCategory: FlagCategory
  ): LightDueOperationType[] {
    // All flag categories now support special operations
    switch (portCallType) {
      case 'simple-port-call':
        return ['port-entry', 'port-exit'];
      case 'bosphorus-transit':
        return ['bosphorus-entry', 'bosphorus-exit'];
      case 'canakkale-transit':
        return ['dardanelles-entry', 'dardanelles-exit'];
      case 'transit-with-port-call':
        return ['bosphorus-entry', 'bosphorus-exit', 'port-entry', 'port-exit'];
      default:
        return ['standard'];
    }
  }

  // Calculate light due for port calls (includes both entry and exit for simple port calls)
  private calculateLightDueForPortCall(
    nrt: number,
    flagCategory: FlagCategory,
    portCallType: string,
    isTransit: boolean = false
  ): any {
    // For all flag categories, simple port calls calculate both entry and exit
    if (!isTransit && portCallType === 'simple-port-call') {
      const entryResult = this.calculateLightDue(nrt, flagCategory, 'port-entry');
      const exitResult = this.calculateLightDue(nrt, flagCategory, 'port-exit');

      const totalLightDue = entryResult.total + exitResult.total;

      return {
        first800NRT: entryResult.first800NRT * 2, // Both entry and exit
        above800NRT: entryResult.above800NRT * 2, // Both entry and exit
        operationType: 'port-entry-exit',
        multiplierUsed: entryResult.multiplierUsed,
        total: totalLightDue,
        calculation: `800 NRT x ${entryResult.multiplierUsed} + ${nrt - 800} NRT x ${entryResult.above800NRT > 0 ? (entryResult.above800NRT / (nrt - 800)).toFixed(5) : '0'} (Port Entry + Exit): ${totalLightDue.toFixed(2)} USD`
      };
    } else {
      // For other cases, use single operation
      const operationType = this.determineLightDueOperationType(portCallType, flagCategory, isTransit);
      return this.calculateLightDue(nrt, flagCategory, operationType);
    }
  }

  // Calculate light due with Turkish flag special rates (single operation)
  private calculateLightDue(
    nrt: number,
    flagCategory: FlagCategory,
    operationType?: LightDueOperationType,
    vesselTrafficFee?: number
  ): any {
    let first800NRT: number;
    let above800NRT: number;
    let multiplierUsed: number;
    let operationDescription: string;

    // Determine operation type if not provided
    const finalOperationType = operationType || 'standard';

    // Special calculation for all flag categories when operation type is not standard
    if (finalOperationType !== 'standard') {
      let highMultiplier: number;
      let lowMultiplier: number;

      if (flagCategory === 'cabotage') {
        // Cabotage flag special multipliers
        switch (finalOperationType) {
          case 'dardanelles-entry':
          case 'dardanelles-exit':
          case 'bosphorus-entry':
          case 'bosphorus-exit':
            highMultiplier = 0.06;
            lowMultiplier = 0.030;
            operationDescription = finalOperationType === 'dardanelles-entry' ? 'Çanakkale Boğazı Giriş' :
                                 finalOperationType === 'dardanelles-exit' ? 'Çanakkale Boğazı Çıkış' :
                                 finalOperationType === 'bosphorus-entry' ? 'İstanbul Boğazı Giriş' :
                                 'İstanbul Boğazı Çıkış';
            break;
          case 'port-entry':
            highMultiplier = 0.0336;
            lowMultiplier = 0.0168;
            operationDescription = 'Liman Giriş';
            break;
          case 'port-exit':
            highMultiplier = 0.0336;
            lowMultiplier = 0.0168;
            operationDescription = 'Liman Çıkış';
            break;
          default:
            // Fallback to standard calculation for cabotage
            highMultiplier = 0.2112;
            lowMultiplier = 0.1056;
            operationDescription = 'Standard';
        }
      } else if (flagCategory === 'turkish') {
        // Turkish flag (non-cabotage) special calculation
        switch (finalOperationType) {
          case 'round-trip-without-port-call':
            highMultiplier = 2.1294;
            lowMultiplier = 1.0647;
            operationDescription = 'Uğrakısız Sefer (Gidiş-Dönüş)';
            break;
          case 'dardanelles-entry':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'Çanakkale Boğazı Giriş';
            break;
          case 'dardanelles-exit':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'Çanakkale Boğazı Çıkış';
            break;
          case 'bosphorus-entry':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'İstanbul Boğazı Giriş';
            break;
          case 'bosphorus-exit':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'İstanbul Boğazı Çıkış';
            break;
          case 'port-entry':
            highMultiplier = 0.118272;
            lowMultiplier = 0.059136;
            operationDescription = 'Liman Giriş';
            break;
          case 'port-exit':
            highMultiplier = 0.118272;
            lowMultiplier = 0.059136;
            operationDescription = 'Liman Çıkış';
            break;
          default:
            // Fallback to standard calculation
            highMultiplier = 0.2112;
            lowMultiplier = 0.1056;
            operationDescription = 'Standard';
        }
      } else {
        // Foreign flag special calculation with new multipliers
        switch (finalOperationType) {
          case 'round-trip-without-port-call':
            highMultiplier = 2.1294;
            lowMultiplier = 1.0647;
            operationDescription = 'Uğrakısız Sefer (Gidiş-Dönüş)';
            break;
          case 'dardanelles-entry':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'Çanakkale Boğazı Giriş';
            break;
          case 'dardanelles-exit':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'Çanakkale Boğazı Çıkış';
            break;
          case 'bosphorus-entry':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'İstanbul Boğazı Giriş';
            break;
          case 'bosphorus-exit':
            highMultiplier = 0.19008;
            lowMultiplier = 0.09504;
            operationDescription = 'İstanbul Boğazı Çıkış';
            break;
          case 'port-entry':
            highMultiplier = 0.16896;
            lowMultiplier = 0.08448;
            operationDescription = 'Liman Giriş';
            break;
          case 'port-exit':
            highMultiplier = 0.16896;
            lowMultiplier = 0.08448;
            operationDescription = 'Liman Çıkış';
            break;
          default:
            // Fallback to standard calculation
            highMultiplier = 0.2112;
            lowMultiplier = 0.1056;
            operationDescription = 'Standard';
        }
      }

      // Calculation: 800 NT'ye kadar ve fazlası için farklı çarpanlar
      if (nrt <= 800) {
        first800NRT = nrt * highMultiplier;
        above800NRT = 0;
        multiplierUsed = highMultiplier;
      } else {
        first800NRT = 800 * highMultiplier;
        above800NRT = (nrt - 800) * lowMultiplier;
        multiplierUsed = highMultiplier; // Primary multiplier for display
      }
    } else {
      // Standard calculation for all flags when operation type is standard
      first800NRT = Math.min(nrt, 800) * 0.2112;
      above800NRT = Math.max(0, nrt - 800) * 0.1056;
      multiplierUsed = 0.2112;
      operationDescription = 'Standard';
    }

    const lightDueTotal = first800NRT + above800NRT;
    const total = lightDueTotal + (vesselTrafficFee || 0);

    let calculation: string;
    if (finalOperationType !== 'standard') {
      // Use English descriptions for PDF compatibility
      let englishOperationDescription: string;
      switch (finalOperationType) {
        case 'round-trip-without-port-call':
          englishOperationDescription = 'Round Trip Without Port Call';
          break;
        case 'dardanelles-entry':
          englishOperationDescription = flagCategory === 'cabotage' ? 'Dardanelles Entry (Cabotage)' :
                                       flagCategory === 'turkish' ? 'Dardanelles Entry (Turkish)' :
                                       'Dardanelles Entry (Foreign)';
          break;
        case 'dardanelles-exit':
          englishOperationDescription = flagCategory === 'cabotage' ? 'Dardanelles Exit (Cabotage)' :
                                       flagCategory === 'turkish' ? 'Dardanelles Exit (Turkish)' :
                                       'Dardanelles Exit (Foreign)';
          break;
        case 'bosphorus-entry':
          englishOperationDescription = flagCategory === 'cabotage' ? 'Bosphorus Entry (Cabotage)' :
                                       flagCategory === 'turkish' ? 'Bosphorus Entry (Turkish)' :
                                       'Bosphorus Entry (Foreign)';
          break;
        case 'bosphorus-exit':
          englishOperationDescription = flagCategory === 'cabotage' ? 'Bosphorus Exit (Cabotage)' :
                                       flagCategory === 'turkish' ? 'Bosphorus Exit (Turkish)' :
                                       'Bosphorus Exit (Foreign)';
          break;
        case 'port-entry':
          englishOperationDescription = flagCategory === 'cabotage' ? 'Port Entry (Cabotage)' :
                                       flagCategory === 'turkish' ? 'Port Entry (Turkish)' :
                                       'Port Entry (Foreign)';
          break;
        case 'port-exit':
          englishOperationDescription = flagCategory === 'cabotage' ? 'Port Exit (Cabotage)' :
                                       flagCategory === 'turkish' ? 'Port Exit (Turkish)' :
                                       'Port Exit (Foreign)';
          break;
        default:
          englishOperationDescription = 'Standard';
      }

      if (nrt <= 800) {
        calculation = `${nrt} NRT x ${multiplierUsed} (${englishOperationDescription}): ${first800NRT.toFixed(2)} USD`;
      } else {
        calculation = `800 NRT x ${multiplierUsed} + ${nrt - 800} NRT x ${above800NRT > 0 ? (above800NRT / (nrt - 800)).toFixed(5) : '0'} (${englishOperationDescription}): ${lightDueTotal.toFixed(2)} USD`;
      }
    } else {
      calculation = `First 800 NRT: ${first800NRT.toFixed(2)} USD + ${Math.max(0, nrt - 800)} NRT x 0.1056: ${above800NRT.toFixed(2)} USD`;
    }

    if (vesselTrafficFee) {
      calculation += ` + Vessel traffic fee: ${vesselTrafficFee} USD`;
    }

    return {
      first800NRT,
      above800NRT,
      vesselTrafficFee,
      operationType: finalOperationType,
      multiplierUsed,
      total,
      calculation
    };
  }

  // Calculate vessel traffic fee
  private calculateVesselTrafficFee(
    portLocation: string,
    nrt: number,
    flagCategory: FlagCategory,
    vesselType: VesselType
  ): any | undefined {
    // Applicable ports for vessel traffic fee (both Turkish and English names)
    const applicablePorts = [
      'izmit', 'kocaeli', 'izmir', 'mersin',
      'İzmit', 'Kocaeli', 'İzmir', 'Mersin'
    ];

    const portLocationLower = portLocation.toLowerCase();
    const isApplicable = applicablePorts.some(port =>
      portLocationLower.includes(port.toLowerCase())
    );

    if (!isApplicable) {
      console.log(`Vessel Traffic Fee not applicable for port location: ${portLocation}`);
      return undefined;
    }

    // Determine rate based on NRT ranges, flag category, and vessel type
    let feeAmount: number;
    let rangeDescription: string;

    // NRT range determination
    if (nrt >= 300 && nrt <= 2000) {
      rangeDescription = '300-2,000 Net Ton';
      if (flagCategory === 'cabotage') {
        feeAmount = 8; // Turkish Cabotage
      } else if (flagCategory === 'turkish') {
        feeAmount = 22; // Turkish Non-Cabotage
      } else { // foreign
        if (vesselType === 'passenger') {
          feeAmount = 70.4; // Foreign Passenger
        } else {
          feeAmount = 88; // Foreign Commercial
        }
      }
    } else if (nrt >= 2001 && nrt <= 5000) {
      rangeDescription = '2,001-5,000 Net Ton';
      if (flagCategory === 'cabotage') {
        feeAmount = 16;
      } else if (flagCategory === 'turkish') {
        feeAmount = 44;
      } else { // foreign
        if (vesselType === 'passenger') {
          feeAmount = 140.8;
        } else {
          feeAmount = 176;
        }
      }
    } else if (nrt >= 5001 && nrt <= 10000) {
      rangeDescription = '5,001-10,000 Net Ton';
      if (flagCategory === 'cabotage') {
        feeAmount = 30;
      } else if (flagCategory === 'turkish') {
        feeAmount = 82.5;
      } else { // foreign
        if (vesselType === 'passenger') {
          feeAmount = 264;
        } else {
          feeAmount = 330;
        }
      }
    } else if (nrt >= 10001 && nrt <= 20000) {
      rangeDescription = '10,001-20,000 Net Ton';
      if (flagCategory === 'cabotage') {
        feeAmount = 45;
      } else if (flagCategory === 'turkish') {
        feeAmount = 123.75;
      } else { // foreign
        if (vesselType === 'passenger') {
          feeAmount = 396;
        } else {
          feeAmount = 495;
        }
      }
    } else if (nrt >= 20001 && nrt <= 50000) {
      rangeDescription = '20,001-50,000 Net Ton';
      if (flagCategory === 'cabotage') {
        feeAmount = 60;
      } else if (flagCategory === 'turkish') {
        feeAmount = 165;
      } else { // foreign
        if (vesselType === 'passenger') {
          feeAmount = 528;
        } else {
          feeAmount = 660;
        }
      }
    } else if (nrt >= 50001) {
      rangeDescription = '50,001+ Net Ton';
      if (flagCategory === 'cabotage') {
        feeAmount = 90;
      } else if (flagCategory === 'turkish') {
        feeAmount = 247.5;
      } else { // foreign
        if (vesselType === 'passenger') {
          feeAmount = 792;
        } else {
          feeAmount = 990;
        }
      }
    } else {
      // NRT below 300 - not applicable
      console.log(`Vessel Traffic Fee not applicable for NRT below 300: ${nrt}`);
      return undefined;
    }

    // Flag and vessel type description for calculation display
    let flagDescription: string;
    if (flagCategory === 'cabotage') {
      flagDescription = 'Turkish Cabotage';
    } else if (flagCategory === 'turkish') {
      flagDescription = 'Turkish Non-Cabotage';
    } else {
      flagDescription = vesselType === 'passenger' ? 'Foreign Passenger' : 'Foreign Commercial';
    }

    console.log(`Vessel Traffic Fee applied for port location: ${portLocation}, NRT: ${nrt}, Flag: ${flagDescription}`);

    return {
      entry: feeAmount,
      departure: feeAmount,
      total: feeAmount * 2, // Entry + Departure
      nrtRange: rangeDescription,
      flagCategory: flagDescription,
      calculation: `${nrt} NRT (${rangeDescription}, ${flagDescription}): ${feeAmount} USD x 2 = ${feeAmount * 2} USD`,
      applicablePorts: applicablePorts.filter(port =>
        portLocationLower.includes(port.toLowerCase())
      )
    };
  }

  // Calculate harbour master fee
  private calculateHarbourMasterFee(nrt: number, usdTryRate: number, flagCategory: FlagCategory): any {
    // Kabotaj bayraklarda harbour master fee hesaplanmaz
    if (flagCategory === 'cabotage') {
      return {
        baseAmountTL: 0,
        usdAmount: 0,
        calculation: 'Not applicable for cabotage flagged vessels'
      };
    }

    let baseAmountTL: number;

    if (nrt <= 500) {
      baseAmountTL = 1079.40;
    } else if (nrt <= 2000) {
      baseAmountTL = 2878.60;
    } else if (nrt <= 4000) {
      baseAmountTL = 5757.20;
    } else if (nrt <= 8000) {
      baseAmountTL = 8635.80;
    } else if (nrt <= 10000) {
      baseAmountTL = 14393.00;
    } else if (nrt <= 30000) {
      baseAmountTL = 28786.00;
    } else if (nrt <= 50000) {
      baseAmountTL = 43179.00;
    } else {
      baseAmountTL = 71965.00;
    }

    const usdAmount = baseAmountTL / usdTryRate;

    return {
      baseAmountTL,
      usdAmount,
      calculation: `${nrt} NRT: ${baseAmountTL} TL (${usdAmount.toFixed(2)} USD)`
    };
  }

  // Calculate chamber of shipping fee (klavuzdaki tablolara göre)
  private calculateChamberOfShippingFee(roundedGRT: number, flagCategory: FlagCategory, vesselType: VesselType, usdTryRate: number): any {
    let amountTL: number;

    // PROFORMA klavuzundaki tablolar (TL cinsinden)
    // Figure 008: Türk bayraklı gemiler için (cabotage ve turkish)
    // Figure 009: Yabancı bayraklı gemiler için (foreign)

    if (flagCategory === 'foreign') {
      // Figure 009 - Foreign flagged ships rates (TL) - official rates
      if (roundedGRT <= 500) {
        if (vesselType === 'passenger') {
          amountTL = 1200; // Yolcu/Kruvaziyer gemileri
        } else {
          amountTL = 1400; // Diğer gemiler
        }
      } else if (roundedGRT <= 1500) {
        if (vesselType === 'passenger') {
          amountTL = 2400; // Yolcu/Kruvaziyer gemileri
        } else {
          amountTL = 2800; // Diğer gemiler
        }
      } else if (roundedGRT <= 2500) {
        if (vesselType === 'passenger') {
          amountTL = 3600; // Yolcu/Kruvaziyer gemileri
        } else {
          amountTL = 4200; // Diğer gemiler
        }
      } else if (roundedGRT <= 5000) {
        if (vesselType === 'passenger') {
          amountTL = 4200; // Yolcu/Kruvaziyer gemileri
        } else {
          amountTL = 4900; // Diğer gemiler
        }
      } else if (roundedGRT <= 10000) {
        if (vesselType === 'passenger') {
          amountTL = 4800; // Yolcu/Kruvaziyer gemileri
        } else {
          amountTL = 5600; // Diğer gemiler
        }
      } else if (roundedGRT <= 25000) {
        amountTL = 6300; // Same for both Yolcu/Kruvaziyer and Diğer gemiler
      } else if (roundedGRT <= 35000) {
        amountTL = 7000; // Same for both
      } else if (roundedGRT <= 50000) {
        amountTL = 7500; // Same for both
      } else {
        amountTL = 8000; // 50001+ GT - same for both
      }
    } else {
      // Figure 008 - Turkish flagged ships rates (cabotage and turkish) (TL)
      if (roundedGRT <= 500) {
        if (vesselType === 'passenger') {
          amountTL = 600; // Yolcu ve Kruvaziyer Gemiler
        } else {
          amountTL = 670; // Diğer Gemiler (genel) - using as default since we don't have bunker tanker type
        }
      } else if (roundedGRT <= 1500) {
        if (vesselType === 'passenger') {
          amountTL = 950; // Yolcu ve Kruvaziyer Gemiler
        } else {
          amountTL = 1120; // Diğer Gemiler
        }
      } else if (roundedGRT <= 2500) {
        if (vesselType === 'passenger') {
          amountTL = 1750; // Yolcu ve Kruvaziyer Gemiler
        } else {
          amountTL = 2050; // Diğer Gemiler
        }
      } else if (roundedGRT <= 5000) {
        if (vesselType === 'passenger') {
          amountTL = 2400; // Yolcu ve Kruvaziyer Gemiler
        } else {
          amountTL = 2800; // Diğer Gemiler
        }
      } else if (roundedGRT <= 10000) {
        if (vesselType === 'passenger') {
          amountTL = 2900; // Yolcu ve Kruvaziyer Gemiler
        } else {
          amountTL = 3400; // Diğer Gemiler
        }
      } else if (roundedGRT <= 25000) {
        amountTL = 4000; // Same for both Yolcu ve Kruvaziyer and Diğer Gemiler
      } else if (roundedGRT <= 35000) {
        amountTL = 4500; // Same for both
      } else if (roundedGRT <= 50000) {
        amountTL = 5000; // Same for both
      } else {
        amountTL = 5300; // 50001 GT ve üzeri - same for both
      }
    }

    const usdAmount = amountTL / usdTryRate;
    const flagDescription = flagCategory === 'foreign' ? 'Foreign Flag (Figure 009)' : 'Turkish Flag (Figure 008)';
    const vesselTypeDescription = vesselType === 'passenger' ? 'Passenger/Cruise' : 'Other Ships';

    return {
      amount: amountTL,
      baseAmountTL: amountTL,
      currency: 'TL' as const,
      usdAmount,
      calculation: `${roundedGRT} GT (${flagDescription}, ${vesselTypeDescription}): ${amountTL.toFixed(2)} TL (${usdAmount.toFixed(2)} USD)`
    };
  }

  // Calculate agency fee (klavuzdaki tabloya göre)
  private calculateAgencyFee(nrt: number, eurUsdRate?: number, isTransit?: boolean): any {
    let amountEUR: number;
    let calculationDetails: string;

    if (isTransit) {
      // Çanakkale ve İstanbul Boğazı Geçiş Ücreti (Bir Boğazdan Bir Geçiş)
      if (nrt <= 1000) {
        amountEUR = 200;
      } else if (nrt <= 2000) {
        amountEUR = 290;
      } else if (nrt <= 3000) {
        amountEUR = 340;
      } else if (nrt <= 4000) {
        amountEUR = 400;
      } else if (nrt <= 5000) {
        amountEUR = 460;
      } else if (nrt <= 7500) {
        amountEUR = 560;
      } else if (nrt <= 10000) {
        amountEUR = 640;
      } else {
        // Büyük tonajlı gemiler için ek ücretler
        let baseAmount = 640; // 7501-10000 NRT base rate
        let additionalAmount = 0;

        if (nrt > 10000) {
          const excessNRT = nrt - 10000;

          if (nrt <= 20000) {
            // 10.001-20.000 NRT: Her 1.000 NRT ve kesri için 30 Euro ek
            const additionalThousands = Math.ceil(excessNRT / 1000);
            additionalAmount += additionalThousands * 30;
          } else if (nrt <= 30000) {
            // 10.001-20.000 NRT: 10 x 30 = 300 Euro
            additionalAmount += 10 * 30;
            // 20.001-30.000 NRT: Her 1.000 NRT ve kesri için 20 Euro ek
            const excessAbove20k = nrt - 20000;
            const additionalThousands = Math.ceil(excessAbove20k / 1000);
            additionalAmount += additionalThousands * 20;
          } else {
            // 10.001-20.000 NRT: 10 x 30 = 300 Euro
            additionalAmount += 10 * 30;
            // 20.001-30.000 NRT: 10 x 20 = 200 Euro
            additionalAmount += 10 * 20;
            // 30.001 NRT ve üzeri: Her 1.000 NRT ve kesri için 10 Euro ek
            const excessAbove30k = nrt - 30000;
            const additionalThousands = Math.ceil(excessAbove30k / 1000);
            additionalAmount += additionalThousands * 10;
          }
        }

        amountEUR = baseAmount + additionalAmount;
      }

      calculationDetails = `${nrt} NRT (Transit): ${amountEUR} EUR`;
    } else {
      // Liman ve Veva Karasularındaki Gemiler
      if (nrt <= 500) {
        amountEUR = 660;
      } else if (nrt <= 1000) {
        amountEUR = 1000;
      } else if (nrt <= 2000) {
        amountEUR = 1500;
      } else if (nrt <= 3000) {
        amountEUR = 1850;
      } else if (nrt <= 4000) {
        amountEUR = 2300;
      } else if (nrt <= 5000) {
        amountEUR = 2750;
      } else if (nrt <= 7500) {
        amountEUR = 3200;
      } else if (nrt <= 10000) {
        amountEUR = 4000;
      } else {
        // Büyük tonajlı gemiler için ek ücretler
        let baseAmount = 4000; // 7501-10000 NRT base rate
        let additionalAmount = 0;

        if (nrt > 10000) {
          const excessNRT = nrt - 10000;

          if (nrt <= 20000) {
            // 10.001-20.000 NRT: Her 1.000 NRT ve kesri için 125 Euro ek
            const additionalThousands = Math.ceil(excessNRT / 1000);
            additionalAmount += additionalThousands * 125;
          } else if (nrt <= 30000) {
            // 10.001-20.000 NRT: 10 x 125 = 1250 Euro
            additionalAmount += 10 * 125;
            // 20.001-30.000 NRT: Her 1.000 NRT ve kesri için 100 Euro ek
            const excessAbove20k = nrt - 20000;
            const additionalThousands = Math.ceil(excessAbove20k / 1000);
            additionalAmount += additionalThousands * 100;
          } else {
            // 10.001-20.000 NRT: 10 x 125 = 1250 Euro
            additionalAmount += 10 * 125;
            // 20.001-30.000 NRT: 10 x 100 = 1000 Euro
            additionalAmount += 10 * 100;
            // 30.001 NRT ve üzeri: Her 1.000 NRT ve kesri için 75 Euro ek
            const excessAbove30k = nrt - 30000;
            const additionalThousands = Math.ceil(excessAbove30k / 1000);
            additionalAmount += additionalThousands * 75;
          }
        }

        amountEUR = baseAmount + additionalAmount;
      }

      calculationDetails = `${nrt} NRT (Port): ${amountEUR} EUR`;
    }

    const usdAmount = eurUsdRate ? amountEUR * eurUsdRate : amountEUR;

    return {
      amount: amountEUR,
      currency: 'EUR' as const,
      usdAmount,
      calculation: `${calculationDetails}${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
    };
  }

  // Calculate freight share fee (klavuzdaki tabloya göre)
  private calculateFreightShare(
    cargoCategory: string,
    tonnageOrUnits: number,
    vesselType: VesselType
  ): any {
    // PROFORMA klavuzundaki CHAMBER OF SHIPPING FREIGHT SHARE tablosu
    // Ro-Ro ve konteyner gemileri için sabit $700 USD
    // Diğer gemiler için taşınan yük miktarına göre aralık bazlı hesaplama
    let currency: 'EUR' | 'USD' = 'USD';
    let freightShareAmount: number;
    let rangeDescription: string;

    // Ro-Ro ve konteyner gemileri için özel tarife
    if (vesselType === 'ro-ro-ro-pax' || vesselType === 'container') {
      freightShareAmount = 700;
      rangeDescription = vesselType === 'ro-ro-ro-pax' ? 'Ro-Ro vessel fixed rate' : 'Container vessel fixed rate';

      return {
        cargoCategory,
        tonnageOrUnits,
        ratePerUnit: 0,
        baseAmount: freightShareAmount,
        finalAmount: freightShareAmount,
        currency,
        usdAmount: freightShareAmount,
        calculation: `${vesselType === 'ro-ro-ro-pax' ? 'Ro-Ro' : 'Container'} vessel: ${freightShareAmount} USD (fixed rate)`
      };
    }

    // Diğer gemi türleri için yük miktarı aralıklarına göre sabit USD ücretleri
    if (tonnageOrUnits <= 20000) {
      freightShareAmount = 650;
      rangeDescription = '0-20,000 ton';
    } else if (tonnageOrUnits <= 40000) {
      freightShareAmount = 1000;
      rangeDescription = '20,001-40,000 ton';
    } else if (tonnageOrUnits <= 60000) {
      freightShareAmount = 1300;
      rangeDescription = '40,001-60,000 ton';
    } else if (tonnageOrUnits <= 100000) {
      freightShareAmount = 1600;
      rangeDescription = '60,001-100,000 ton';
    } else {
      freightShareAmount = 2000;
      rangeDescription = '100,001+ ton';
    }

    return {
      cargoCategory,
      tonnageOrUnits,
      ratePerUnit: 0, // Artık ton başına oran yok, sabit ücret
      baseAmount: freightShareAmount,
      finalAmount: freightShareAmount,
      currency,
      usdAmount: freightShareAmount, // Zaten USD cinsinden
      calculation: `${tonnageOrUnits} ton (${rangeDescription}): ${freightShareAmount} USD`
    };
  }

  // Helper function to calculate progressive rates for tiered cargo categories
  private calculateProgressiveRate(tonnage: number, tiers: Array<{limit: number, rate: number}>): {amount: number, breakdown: string} {
    let totalAmount = 0;
    let remainingTonnage = tonnage;
    let breakdown = '';

    for (let i = 0; i < tiers.length; i++) {
      const tier = tiers[i];
      const isLastTier = i === tiers.length - 1;

      if (remainingTonnage <= 0) break;

      let tierTonnage: number;
      if (isLastTier) {
        // Last tier takes all remaining tonnage
        tierTonnage = remainingTonnage;
      } else {
        // Calculate tonnage for this tier
        const previousLimit = i > 0 ? tiers[i-1].limit : 0;
        const tierCapacity = tier.limit - previousLimit;
        tierTonnage = Math.min(remainingTonnage, tierCapacity);
      }

      const tierAmount = tierTonnage * tier.rate;
      totalAmount += tierAmount;

      if (breakdown) breakdown += ' + ';
      if (isLastTier && remainingTonnage > 0) {
        breakdown += `${tierTonnage} ton x ${tier.rate} EUR`;
      } else {
        const rangeStart = i > 0 ? tiers[i-1].limit + 1 : 0;
        const rangeEnd = tier.limit;
        breakdown += `${tierTonnage} ton (${rangeStart}-${rangeEnd}) x ${tier.rate} EUR`;
      }

      remainingTonnage -= tierTonnage;
    }

    return { amount: totalAmount, breakdown };
  }

  // Calculate attendance/supervision fee (klavuzdaki detaylı tablolara göre)
  private calculateAttendanceSupervisionFee(
    cargoCategory: string,
    tonnageOrUnits: number,
    eurUsdRate?: number
  ): any {
    let baseAmount: number;
    let currency: 'EUR' | 'USD' = 'EUR';
    let categoryDescription: string;
    let calculationBreakdown: string;

    // PROFORMA klavuzundaki GÖZETİM HİZMETLERİ tablosu
    // Turkish Port Authority Official Tariff Structure
    switch (cargoCategory?.toLowerCase()) {
      // A) DÖKME EŞYA (Bulk Cargo)
      case 'bulk-solid-minerals': // A-a) Katı Eşya
        const solidMineralsTiers = [
          { limit: 10000, rate: 0.15 },
          { limit: 20000, rate: 0.10 },
          { limit: Infinity, rate: 0.05 }
        ];
        const solidMineralsResult = this.calculateProgressiveRate(tonnageOrUnits, solidMineralsTiers);
        baseAmount = solidMineralsResult.amount;
        calculationBreakdown = solidMineralsResult.breakdown;
        categoryDescription = 'A-a) Katı Eşya (Maden cevheri, mineraller, hurda, pik demiri, kömür, keçi boynuzu, hayvan yemi, küspe, çimento, kilinker, ponza, suni gübre, mucur)';
        break;

      case 'bulk-grains': // A-b) Tahıl ve Tohumlar
        const grainsTiers = [
          { limit: 10000, rate: 0.10 },
          { limit: 25000, rate: 0.075 },
          { limit: Infinity, rate: 0.045 }
        ];
        const grainsResult = this.calculateProgressiveRate(tonnageOrUnits, grainsTiers);
        baseAmount = grainsResult.amount;
        calculationBreakdown = grainsResult.breakdown;
        categoryDescription = 'A-b) Tahıl ve Tohumlar (Buğday, arpa, yulaf, çavdar, pirinç, mısır, ayçiçeği, soya fasulyesi, fığ)';
        break;

      case 'bulk-legumes': // A-c) Bakliyat
        const legumesTiers = [
          { limit: 5000, rate: 0.30 },
          { limit: Infinity, rate: 0.15 }
        ];
        const legumesResult = this.calculateProgressiveRate(tonnageOrUnits, legumesTiers);
        baseAmount = legumesResult.amount;
        calculationBreakdown = legumesResult.breakdown;
        categoryDescription = 'A-c) Bakliyat (Bakla, börülce, fasulye, mercimek, nohut)';
        break;

      case 'bulk-petroleum': // A-d) Ham petrol, akaryakıt
        const petroleumTiers = [
          { limit: 15000, rate: 0.040 },
          { limit: 35000, rate: 0.030 },
          { limit: Infinity, rate: 0.015 }
        ];
        const petroleumResult = this.calculateProgressiveRate(tonnageOrUnits, petroleumTiers);
        baseAmount = petroleumResult.amount;
        calculationBreakdown = petroleumResult.breakdown;
        categoryDescription = 'A-d) Ham petrol, akaryakıt';
        break;

      case 'bulk-gas': // A-e) LPG ve LNG Gazlar
        const gasTiers = [
          { limit: 15000, rate: 0.15 },
          { limit: Infinity, rate: 0.05 }
        ];
        const gasResult = this.calculateProgressiveRate(tonnageOrUnits, gasTiers);
        baseAmount = gasResult.amount;
        calculationBreakdown = gasResult.breakdown;
        categoryDescription = 'A-e) LPG ve LNG Gazlar';
        break;

      case 'bulk-chemicals': // A-f) Kimyevi maddeler
        baseAmount = tonnageOrUnits * 0.15;
        calculationBreakdown = `${tonnageOrUnits} ton x 0.15 EUR`;
        categoryDescription = 'A-f) Kimyevi maddeler (Petrol ürünü olanlar dahil) Şarap, zeytinyağı, melas, yemeklik sıvı yağ, madeni yağ, donyağı';
        break;

      // B) DÖKME OLMAYAN EŞYA (Non-Bulk Cargo)
      case 'non-bulk-grains': // B-a) Tahıl ve un, suni gübre, şeker, çimento, pirinç, irmik, keçi boynuzu, mineraller, mermer blok
        const nonBulkGrainsTiers = [
          { limit: 20000, rate: 0.15 },
          { limit: Infinity, rate: 0.05 }
        ];
        const nonBulkGrainsResult = this.calculateProgressiveRate(tonnageOrUnits, nonBulkGrainsTiers);
        baseAmount = nonBulkGrainsResult.amount;
        calculationBreakdown = nonBulkGrainsResult.breakdown;
        categoryDescription = 'B-a) Tahıl ve un, suni gübre, şeker, çimento, pirinç, irmik, keçi boynuzu, mineraller, mermer blok';
        break;

      case 'fresh-produce': // B-b) Taze meyve ve sebze, narenciye, dondurulmuş gıda
        baseAmount = tonnageOrUnits * 1.00;
        calculationBreakdown = `${tonnageOrUnits} ton x 1.00 EUR`;
        categoryDescription = 'B-b) Taze meyve ve sebze, narenciye, dondurulmuş gıda';
        break;

      case 'non-bulk-legumes': // B-c) Bakliyat ve Tohumlar
        baseAmount = tonnageOrUnits * 0.60;
        calculationBreakdown = `${tonnageOrUnits} ton x 0.60 EUR`;
        categoryDescription = 'B-c) Bakliyat ve Tohumlar';
        break;

      case 'steel-paper': // B-d) Kâğıt ve Demir çelik mamulü ile yarı mamulleri
        const steelPaperTiers = [
          { limit: 5000, rate: 0.25 },
          { limit: 10000, rate: 0.15 },
          { limit: Infinity, rate: 0.10 }
        ];
        const steelPaperResult = this.calculateProgressiveRate(tonnageOrUnits, steelPaperTiers);
        baseAmount = steelPaperResult.amount;
        calculationBreakdown = steelPaperResult.breakdown;
        categoryDescription = 'B-d) Kâğıt ve Demir çelik mamulü ile yarı mamulleri (Saç levha, kangal demir, profil, kütük demir, inşaat demiri, firkete demir, filmaşin, her türlü boru, rulo saç, gazete kağıdı, rulo kağıt, kağıt hamuru)';
        break;

      case 'wood-logs': // B-e) Ağaç kütüğü ve tomruk
        const woodLogsTiers = [
          { limit: 3000, rate: 0.50 },
          { limit: 5000, rate: 0.35 },
          { limit: Infinity, rate: 0.10 }
        ];
        const woodLogsResult = this.calculateProgressiveRate(tonnageOrUnits, woodLogsTiers);
        baseAmount = woodLogsResult.amount;
        calculationBreakdown = woodLogsResult.breakdown;
        categoryDescription = 'B-e) Ağaç kütüğü ve tomruk';
        break;

      // C) BOŞ KONTEYNER VE BOŞ TREYLER
      case 'empty-containers': // C) Boş konteyner ve boş treyler (Euro/Adet) - Yıllık 25.000 adet boş konteyner üzerinde işlem yapan hatlar bu ücretten muaftır
        baseAmount = tonnageOrUnits * 10.00; // EUR/Adet
        calculationBreakdown = `${tonnageOrUnits} adet x 10.00 EUR`;
        categoryDescription = 'C) Boş Konteyner ve Boş Treyler (Euro/Adet) - Yıllık 25.000 adet boş konteyner üzerinde işlem yapan hatlar bu ücretten muaftır';
        break;

      // D) CANLI HAYVANLAR
      case 'livestock-small': // D-a) Küçükbaş
        baseAmount = tonnageOrUnits * 0.05; // EUR/Adet
        calculationBreakdown = `${tonnageOrUnits} adet x 0.05 EUR`;
        categoryDescription = 'D-a) Küçükbaş (Euro/Adet)';
        break;

      case 'livestock-large': // D-b) Büyükbaş
        baseAmount = tonnageOrUnits * 0.15; // EUR/Adet
        calculationBreakdown = `${tonnageOrUnits} adet x 0.15 EUR`;
        categoryDescription = 'D-b) Büyükbaş (Euro/Adet)';
        break;

      // E) DİĞER EŞYA, KIRKAMBAR OLARAK TAŞINAN YÜKLER
      case 'other-cargo-carrier-pays': // E-I) Yükleme ve/veya boşaltma ücretlerinin taşıyan tarafından ödenmesi halinde
        baseAmount = tonnageOrUnits * 1.00;
        calculationBreakdown = `${tonnageOrUnits} ton x 1.00 EUR`;
        categoryDescription = 'E-I) Diğer Eşya, Kırkambar Olarak Taşınan Yükler - Yükleme ve/veya boşaltma ücretlerinin taşıyan tarafından ödenmesi halinde';
        break;

      case 'other-cargo-shipper-pays': // E-II) Yükleme ve/veya boşaltma ücretlerinin yükleyici ve/veya alıcı tarafından ödenmesi halinde
        baseAmount = tonnageOrUnits * 0.60;
        calculationBreakdown = `${tonnageOrUnits} ton x 0.60 EUR`;
        categoryDescription = 'E-II) Diğer Eşya, Kırkambar Olarak Taşınan Yükler - Yükleme ve/veya boşaltma ücretlerinin yükleyici ve/veya alıcı tarafından ödenmesi halinde';
        break;

      // F) KONTEYNER İÇİNDE TAŞINAN TÜM YÜKLER
      case 'containers': // F) Konteyner içinde taşınan tüm yükler (Yukarıdakiler dâhil) euro / adet
        baseAmount = tonnageOrUnits * 15.00; // EUR/Adet
        calculationBreakdown = `${tonnageOrUnits} adet x 15.00 EUR`;
        categoryDescription = 'F) Konteyner İçinde Taşınan Tüm Yükler (Yukarıdakiler dâhil) euro/adet';
        break;

      case 'transit-containers': // F) Gemi ile yurt dışından gelip başka gemi ile yurt dışına giden transit konteyner (euro / adet) - Yıllık 50.000 adet dolu konteyner üzerinde işlem yapan hatlar bu ücretten muaftır
        baseAmount = tonnageOrUnits * 15.00; // EUR/Adet
        calculationBreakdown = `${tonnageOrUnits} adet x 15.00 EUR`;
        categoryDescription = 'F) Gemi ile yurt dışından gelip başka gemi ile yurt dışına giden transit konteyner (euro/adet) - Yıllık 50.000 adet dolu konteyner üzerinde işlem yapan hatlar bu ücretten muaftır';
        break;

      // G) OTOMOBİL, JEEP, PİKAP, PANELVAn, MİNİBÜS, MİDİBÜS EURO /Beher Adet
      case 'vehicles': // G) Yükleme ve/veya boşaltma ücretlerinin taşıyan, yükleyici ve/veya alıcı tarafından ödenmesi halinde
        const vehiclesTiers = [
          { limit: 50, rate: 5.00 },    // 0 - 50 adet'e kadar
          { limit: 300, rate: 3.00 },   // 51 - 300 adet'e kadar
          { limit: Infinity, rate: 1.50 } // 301 ve üzeri
        ];
        const vehiclesResult = this.calculateProgressiveRate(tonnageOrUnits, vehiclesTiers);
        baseAmount = vehiclesResult.amount;
        calculationBreakdown = vehiclesResult.breakdown.replace(/ton/g, 'adet');
        categoryDescription = 'G) Otomobil, Jeep, Pikap, Panelvan, Minibüs, Midibüs EURO/Beher Adet - Yükleme ve/veya boşaltma ücretlerinin taşıyan, yükleyici ve/veya alıcı tarafından ödenmesi halinde';
        break;

      // H) RO - RO GEMİLERLE TAŞINAN VE KENDİ KENDİNE YÜRÜYEBİLEN TEKERLEKLİ PALETLİ VASITA VE İŞ MAKİNALARI EURO / Beher Metre
      case 'ro-ro-vehicles': // H) Euro / Uzunluğu üzerinden beher metre için
        baseAmount = tonnageOrUnits * 3.00; // EUR/Metre
        calculationBreakdown = `${tonnageOrUnits} metre x 3.00 EUR`;
        categoryDescription = 'H) RO-RO Gemilerle Taşınan ve Kendi Kendine Yürüyebilen Tekerlekli Paletli Vasıta ve İş Makinaları EURO/Beher Metre - Euro/Uzunluğu üzerinden beher metre için';
        break;

      default:
        baseAmount = tonnageOrUnits * 0.60; // Varsayılan oran
        calculationBreakdown = `${tonnageOrUnits} ton x 0.60 EUR`;
        categoryDescription = 'Genel yük';
        break;
    }

    const minAmount = 300; // EUR (klavuzda belirtilen minimum)
    const maxAmount = 10000; // EUR (klavuzda belirtilen maksimum)
    const finalAmount = Math.max(minAmount, Math.min(maxAmount, baseAmount));
    const usdAmount = eurUsdRate ? finalAmount * eurUsdRate : finalAmount;

    // Determine unit type based on cargo category
    let unitType = 'ton'; // default
    if (['containers', 'transit-containers', 'vehicles', 'empty-containers', 'livestock-small', 'livestock-large'].includes(cargoCategory || '')) {
      unitType = 'adet';
    } else if (cargoCategory === 'ro-ro-vehicles') {
      unitType = 'metre';
    }

    return {
      cargoCategory: categoryDescription,
      tonnageOrUnits,
      ratePerUnit: 0, // Not applicable for progressive rates
      baseAmount,
      minAmount,
      maxAmount,
      finalAmount,
      currency,
      usdAmount,
      calculation: `${calculationBreakdown} = ${baseAmount.toFixed(2)} EUR (min: ${minAmount}, max: ${maxAmount}) = ${finalAmount.toFixed(2)} EUR${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
    };
  }

  // Calculate container dangerous cargo fee (TEU-based)
  private calculateContainerDangerousCargoFee(
    teuCount: number,
    eurUsdRate?: number
  ): any {
    // Konteyner gemileri için tehlikeli yük ücreti: TEU başına sabit ücret
    // Liman tarifelerinde "Container Dangerous Cargo Fee per TEU" olarak geçer
    const ratePerTEU = 25.00; // EUR per TEU - standart liman tarifesi
    const currency = 'EUR';

    const totalAmount = teuCount * ratePerTEU;
    const usdAmount = eurUsdRate ? totalAmount * eurUsdRate : totalAmount;

    return {
      teuCount,
      ratePerTEU,
      totalAmount,
      currency,
      usdAmount,
      calculation: `${teuCount} TEU x ${ratePerTEU} EUR/TEU = ${totalAmount.toFixed(2)} EUR${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
    };
  }

  // Calculate transit pilotage fee for Bosphorus/Çanakkale
  private calculateTransitPilotage(
    roundedGRT: number,
    vesselType: VesselType,
    isDangerous: boolean
  ): any {
    const baseRateFirstGT = 550; // USD for first 1000 GT
    const additionalRatePerGT = 100; // USD for each additional 1000 GT
    const numberOfTransits = 2; // Istanbul + Çanakkale

    const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
    const baseAmount = (baseRateFirstGT + (additionalGT * additionalRatePerGT)) * numberOfTransits;

    // Tehlikeli yük çarpanı: 1.30 ile çarpma (klavuza göre)
    const total = isDangerous ? baseAmount * 1.30 : baseAmount;
    const dangerousSurcharge = isDangerous ? total - baseAmount : 0;

    return {
      baseAmount,
      numberOfTransits,
      dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
      total,
      calculation: `${roundedGRT/1000} GT: First 1000 GT ${baseRateFirstGT} USD${additionalGT > 0 ? ` + ${additionalGT} x ${additionalRatePerGT} USD` : ''} x ${numberOfTransits} pilots${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}`
    };
  }

  // Calculate transit tugboat fee (only for specific vessel types/conditions)
  private calculateTransitTugboat(
    roundedGRT: number,
    vesselType: VesselType,
    vesselLength: number,
    isDangerous: boolean
  ): any | undefined {
    // Transit tugboat only applies to RORO/Container/LNG Tanker/Bunker Tanker/Other vessels 200-300m OR dangerous cargo
    const requiresTugboat = (
      (vesselLength >= 200 && vesselLength <= 300 &&
       ['ro-ro-ro-pax', 'container', 'lng-tanker', 'bunker-tanker', 'other'].includes(vesselType)) ||
      isDangerous
    );

    if (!requiresTugboat) return undefined;

    // Use same tugboat calculation logic as port calls
    return this.calculateTugboatFee(roundedGRT, vesselType, 'foreign', isDangerous);
  }

  // Calculate transit-only fees (no port call)
  private calculateTransitOnly(
    input: IMaritimeCalculationInput,
    roundedGRT: number
  ): any {
    const isDangerous = Boolean(input.cargoType === 'dangerous' || input.isDangerous);

    const transitPilotage = this.calculateTransitPilotage(roundedGRT, input.vesselType, isDangerous);
    const transitSanitary = this.calculateSanitaryFee(input.netRegisterTonnage, input.flagCategory, input.usdTryRate, true);
    const transitLight = this.calculateLightDueForPortCall(input.netRegisterTonnage, input.flagCategory, input.portCallType, true);
    const transitAgency = this.calculateAgencyFee(input.netRegisterTonnage, input.eurUsdRate, true);

    let transitTugboat;
    if (input.deadweightTonnage) { // Assuming vessel length can be estimated from DWT
      const estimatedLength = Math.min(300, Math.max(150, input.deadweightTonnage / 100)); // Rough estimation
      transitTugboat = this.calculateTransitTugboat(roundedGRT, input.vesselType, estimatedLength, isDangerous);
    }

    const transitTotal =
      transitPilotage.total +
      (transitTugboat?.total || 0) +
      transitSanitary.usdAmount +
      transitLight.total +
      transitAgency.usdAmount;

    return {
      transitPilotage: transitPilotage.total,
      transitTugboat: transitTugboat?.total || 0,
      transitSanitary: transitSanitary.usdAmount,
      transitLight: transitLight.total,
      transitAgency: transitAgency.usdAmount,
      total: transitTotal
    };
  }

  // Main calculation method
  public async calculateMaritimeProforma(
    userId: string,
    input: IMaritimeCalculationInput
  ): Promise<IMaritimeCalculationResult> {
    
    // Validate input
    const validationErrors = this.validateInput(input);
    if (validationErrors.length > 0) {
      throw new Error(`Geçersiz giriş verileri: ${validationErrors.join(', ')}`);
    }

    // Round tonnages
    const roundedGRT = this.roundToNext1000(input.grossRegisterTonnage);
    const roundedNRT = this.roundToNext1000(input.netRegisterTonnage);

    // Check if dangerous cargo
    const isDangerous = Boolean(input.cargoType === 'dangerous' || input.isDangerous);

    // Check if İzmit Körfezi
    const isIzmitKorfezi = Boolean(input.isIzmitKorfezi ||
      input.portLocation.toLowerCase().includes('izmit') ||
      input.portLocation.toLowerCase().includes('kocaeli') ||
      input.portName.toLowerCase().includes('izmit'));

    const calculationNotes: string[] = [];
    
    if (roundedGRT !== input.grossRegisterTonnage) {
      calculationNotes.push(`GRT rounded from ${input.grossRegisterTonnage} to ${roundedGRT}`);
    }
    if (roundedNRT !== input.netRegisterTonnage) {
      calculationNotes.push(`NRT rounded from ${input.netRegisterTonnage} to ${roundedNRT}`);
    }
    if (isDangerous) {
      calculationNotes.push('Dangerous cargo surcharge of 30% applied');
    }
    if (isIzmitKorfezi) {
      calculationNotes.push('İzmit Körfezi Etap pilotaj tarifesi uygulandı');
    }

    // Handle transit-only calculations
    if (input.portCallType === 'bosphorus-transit' || input.portCallType === 'canakkale-transit') {
      const transitFees = this.calculateTransitOnly(input, roundedGRT);

      const fees: IFeeBreakdown = {
        pilotage: { baseAmount: 0, numberOfPilots: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        tugboat: { numberOfTugs: 0, ratePerTug: 0, baseAmount: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        mooring: { berthingFee: 0, unberthingFee: 0, baseAmount: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        quayDue: { dailyRate: 0, days: 0, baseAmount: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        garbageCollection: { amount: 0, currency: 'EUR' as const, usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        sanitaryFee: this.calculateSanitaryFee(input.netRegisterTonnage, input.flagCategory, input.usdTryRate, true),
        lightDue: this.calculateLightDue(input.netRegisterTonnage, input.flagCategory, 'standard'),
        harbourMasterFee: { baseAmountTL: 0, usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        chamberOfShipping: { amount: 0, currency: 'EUR' as const, usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        agencyFee: this.calculateAgencyFee(input.netRegisterTonnage, input.eurUsdRate, true),
        attendanceSupervisionFee: { cargoCategory: 'geçiş', tonnageOrUnits: 0, ratePerUnit: 0, baseAmount: 0, minAmount: 0, maxAmount: 0, finalAmount: 0, currency: 'EUR' as const, usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
        transitFees
      };

      return {
        input,
        roundedGRT,
        roundedNRT,
        fees,
        subtotals: {
          portCallServices: 0,
          transitServices: transitFees.total
        },
        dangerousCargoSurcharge: isDangerous,
        grandTotal: transitFees.total,
        currency: 'USD',
        calculationNotes
      };
    }

    // Calculate all port call fees
    const pilotage = this.calculatePilotageFee(roundedGRT, input.vesselType, input.flagCategory, isIzmitKorfezi, isDangerous);
    const tugboat = this.calculateTugboatFee(roundedGRT, input.vesselType, input.flagCategory, isDangerous);
    const mooring = this.calculateMooringFee(roundedGRT, input.flagCategory, input.vesselType, isDangerous);
    const quayDue = this.calculateQuayDue(roundedGRT, input.flagCategory, input.vesselType, input.daysAtQuay, isDangerous);
    const garbageCollection = this.calculateGarbageCollectionFee(roundedGRT, input.eurUsdRate);
    const sanitaryFee = this.calculateSanitaryFee(input.netRegisterTonnage, input.flagCategory, input.usdTryRate);
    const vesselTrafficFee = this.calculateVesselTrafficFee(input.portLocation, input.netRegisterTonnage, input.flagCategory, input.vesselType);
    const lightDue = this.calculateLightDueForPortCall(input.netRegisterTonnage, input.flagCategory, input.portCallType, false);
    const harbourMasterFee = this.calculateHarbourMasterFee(input.netRegisterTonnage, input.usdTryRate, input.flagCategory);
    const chamberOfShipping = this.calculateChamberOfShippingFee(roundedGRT, input.flagCategory, input.vesselType, input.usdTryRate);
    const agencyFee = this.calculateAgencyFee(input.netRegisterTonnage, input.eurUsdRate, false);

    let freightShare;
    let attendanceSupervisionFee;
    if (input.cargoQuantity && input.cargoCategory) {
      freightShare = this.calculateFreightShare(
        input.cargoCategory,
        input.cargoQuantity,
        input.vesselType
      );
      attendanceSupervisionFee = this.calculateAttendanceSupervisionFee(
        input.cargoCategory,
        input.cargoQuantity,
        input.eurUsdRate
      );
    }

    // Handle transit with port call
    let transitFees;
    if (input.portCallType === 'transit-with-port-call') {
      transitFees = this.calculateTransitOnly(input, roundedGRT);
    }

    // Create fee breakdown
    const fees: IFeeBreakdown = {
      pilotage,
      tugboat,
      mooring,
      quayDue,
      garbageCollection,
      sanitaryFee,
      lightDue,
      vesselTrafficFee,
      harbourMasterFee,
      chamberOfShipping,
      freightShare: freightShare || {
        amount: 0,
        currency: 'USD' as const,
        usdAmount: 0,
        calculation: 'Yük bilgisi eksik'
      },
      agencyFee,
      attendanceSupervisionFee: attendanceSupervisionFee || {
        cargoCategory: input.cargoCategory || 'genel',
        tonnageOrUnits: input.cargoQuantity || 0,
        ratePerUnit: 0,
        baseAmount: 0,
        minAmount: 300,
        maxAmount: 10000,
        finalAmount: 300,
        currency: 'EUR' as const,
        usdAmount: input.eurUsdRate ? 300 * input.eurUsdRate : 300,
        calculation: 'Minimum 300 EUR uygulandı'
      },
      transitFees
    };

    // Calculate subtotals
    const portCallServices =
      pilotage.total +
      tugboat.total +
      mooring.total +
      quayDue.total +
      garbageCollection.usdAmount +
      sanitaryFee.usdAmount +
      lightDue.total +
      (vesselTrafficFee?.total || 0) +
      harbourMasterFee.usdAmount +
      chamberOfShipping.usdAmount +
      (freightShare?.usdAmount || 0) +
      agencyFee.usdAmount +
      (attendanceSupervisionFee?.usdAmount || 0);

    const transitServicesTotal = transitFees?.total || 0;
    const grandTotal = portCallServices + transitServicesTotal;

    return {
      input,
      roundedGRT,
      roundedNRT,
      fees,
      subtotals: {
        portCallServices,
        transitServices: transitServicesTotal > 0 ? transitServicesTotal : undefined
      },
      dangerousCargoSurcharge: isDangerous,
      grandTotal,
      currency: 'USD',
      calculationNotes
    };
  }

  // Save calculation
  public async saveCalculation(
    userId: string,
    result: IMaritimeCalculationResult,
    title?: string,
    description?: string
  ): Promise<ICalculation> {
    const calculation = new Calculation({
      userId,
      calculationType: 'maritime-proforma',
      maritimeInput: result.input,
      maritimeResult: result,
      title: title || `${result.input.portName} - ${result.input.vesselType} Hesaplama`,
      description,
      calculationDate: new Date()
    });

    return await calculation.save();
  }

  // Get user calculations
  public async getUserCalculations(
    userId: string,
    skip: number = 0,
    limit: number = 10
  ): Promise<{ calculations: ICalculation[], total: number }> {
    const calculations = await Calculation
      .find({ userId })
      .sort({ calculationDate: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const total = await Calculation.countDocuments({ userId });

    return { calculations, total };
  }

  // Get calculation by ID
  public async getCalculationById(
    calculationId: string,
    userId: string
  ): Promise<ICalculation | null> {
    return await Calculation.findOne({ 
      _id: calculationId, 
      userId 
    }).lean();
  }

  // Delete calculation
  public async deleteCalculation(
    calculationId: string,
    userId: string
  ): Promise<boolean> {
    const result = await Calculation.deleteOne({
      _id: calculationId,
      userId
    });

    return result.deletedCount > 0;
  }


}