import express from 'express';
import { CalculationController } from './controller';
import { auth } from '../../middleware/auth';
import { validateRequest, maritimeCalculationInputSchema, saveCalculationSchema } from './validation';

const router = express.Router();
const calculationController = new CalculationController();

// Maritime calculation routes
router.post(
  '/maritime/calculate',
  auth,
  validateRequest(maritimeCalculationInputSchema),
  calculationController.calculateMaritimeProforma
);

// Test route without auth (for development/testing only)
router.post(
  '/maritime/test',
  validateRequest(maritimeCalculationInputSchema),
  calculationController.calculateMaritimeProformaTest
);

router.post(
  '/save',
  auth,
  validateRequest(saveCalculationSchema),
  calculationController.saveCalculation
);

router.get(
  '/user',
  auth,
  calculationController.getUserCalculations
);

router.get(
  '/stats',
  auth,
  CalculationController.getCalculationStats
);

router.get(
  '/:id',
  auth,
  calculationController.getCalculationById
);

router.delete(
  '/:id',
  auth,
  calculationController.deleteCalculation
);

// Utility routes
router.get(
  '/utils/ports',
  auth,
  calculationController.getPortSuggestions
);

router.get(
  '/utils/exchange-rates',
  auth,
  calculationController.getExchangeRates
);

router.get(
  '/utils/vessel-types',
  auth,
  calculationController.getVesselTypes
);

router.get(
  '/utils/cargo-types',
  auth,
  calculationController.getCargoTypes
);

router.get(
  '/utils/port-call-types',
  auth,
  calculationController.getPortCallTypes
);

router.get(
  '/utils/flag-categories',
  auth,
  calculationController.getFlagCategories
);

router.get(
  '/utils/cargo-categories',
  auth,
  calculationController.getCargoCategories
);

// PDF generation route
router.post(
  '/generate-pdf',
  auth,
  calculationController.generateCalculationPDF
);

// Legacy routes (kept for backward compatibility)
router.post('/', auth, CalculationController.createCalculation);
router.get('/', auth, CalculationController.getCalculations);
router.get('/:id/legacy', auth, CalculationController.getCalculationById);
router.put('/:id', auth, CalculationController.updateCalculation);
router.delete('/:id/legacy', auth, CalculationController.deleteCalculation);

export default router; 